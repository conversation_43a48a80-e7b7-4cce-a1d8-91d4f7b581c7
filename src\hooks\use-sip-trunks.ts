import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuthStore } from '@/store/auth';
import { createInboundTrunkQueryOptions, createOutboundTrunkQueryOptions } from '@/query-options/sip-client';

export const sipTrunkQueryKeys = {
  inbound: (tenantKey?: string | null) => ['sip-trunks', 'inbound', tenantKey] as const,
  outbound: (tenantKey?: string | null) => ['sip-trunks', 'outbound', tenantKey] as const,
};

export interface SipInboundTrunk {
  sipTrunkId: string;
  name: string;
  numbers: string[];
  metadata?: Record<string, any>;
}

export interface SipOutboundTrunk {
  sipTrunkId: string;
  name: string;
  address: string;
  numbers: string[];
  authUsername?: string;
  authPassword?: string;
  metadata?: Record<string, any>;
}

export interface CreateInboundTrunkRequest {
  name: string;
  numbers: string[];
  tenantName?: string;
}

export interface CreateOutboundTrunkRequest {
  name: string;
  address: string;
  numbers: string[];
  authUsername?: string;
  authPassword?: string;
  tenantName?: string;
}

const sipTrunkApi = {
  async getInboundTrunks(): Promise<SipInboundTrunk[]> {
    const response = await fetch('/api/inbound-trunk');
    if (!response.ok) {
      throw new Error('Failed to fetch inbound trunks');
    }
    return response.json();
  },

  async getOutboundTrunks(): Promise<SipOutboundTrunk[]> {
    const response = await fetch('/api/outbound-trunk');
    if (!response.ok) {
      throw new Error('Failed to fetch outbound trunks');
    }
    return response.json();
  },

  async createInboundTrunk(data: CreateInboundTrunkRequest): Promise<SipInboundTrunk> {
    const response = await fetch('/api/inbound-trunk', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Failed to create inbound trunk');
    }
    return response.json();
  },

  async createOutboundTrunk(data: CreateOutboundTrunkRequest): Promise<SipOutboundTrunk> {
    const response = await fetch('/api/outbound-trunk', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Failed to create outbound trunk');
    }
    return response.json();
  },
};

export const useInboundTrunks = () => {
  const queryClient = useQueryClient();
  const { selectedTenant } = useAuthStore();

  const query = useQuery(createInboundTrunkQueryOptions(selectedTenant?.key || null));

  const createMutation = useMutation({
    mutationFn: sipTrunkApi.createInboundTrunk,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["listSipInboundTrunk"] });
      toast.success('Inbound trunk created successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create inbound trunk');
    },
  });

  return {
    trunks: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    createTrunk: createMutation.mutate,
    isCreating: createMutation.isPending,
  };
};


export const useOutboundTrunks = () => {
  const queryClient = useQueryClient();
  const { selectedTenant } = useAuthStore();

  const query = useQuery(createOutboundTrunkQueryOptions(selectedTenant?.key || null));

  const createMutation = useMutation({
    mutationFn: sipTrunkApi.createOutboundTrunk,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["listSipOutboundTrunk"] });
      toast.success('Outbound trunk created successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create outbound trunk');
    },
  });

  return {
    trunks: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    createTrunk: createMutation.mutate,
    isCreating: createMutation.isPending,
  };
};

// Combined hook for both inbound and outbound trunks
export const useSipTrunks = () => {
  const inbound = useInboundTrunks();
  const outbound = useOutboundTrunks();

  return {
    inbound,
    outbound,
    isLoading: inbound.isLoading || outbound.isLoading,
    refetchAll: () => {
      inbound.refetch();
      outbound.refetch();
    },
  };
};
