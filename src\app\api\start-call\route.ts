import { AgentDispatchClient } from "livekit-server-sdk";
import { NextResponse } from "next/server";
import { envs } from "@/lib/constants/envs";

export const revalidate = 0;

export async function POST(request: Request) {
  const body = await request.json();
  const dispatchClient = new AgentDispatchClient(
    envs.livekitUrl,
    envs.livekitApiKey,
    envs.livekitApiSecret
  );
  const roomName = `call-${Date.now()}`;
  const agentName = "voiceai-agent";
  const metadata = {
    ...body.metadata,
    phonenumber: body.phonenumber,
    outbound_trunk_id: body.outbound_trunk_id
  };
  await dispatchClient.createDispatch(roomName, agentName, {
    metadata: JSON.stringify(metadata),
  });
  return NextResponse.json({ success: true });
}
