import { useState } from "react";
import { <PERSON><PERSON>, Di<PERSON>Trigger, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Info, Trash } from "lucide-react";

interface OutboundCallDialogProps {
  t: any;
}

export default function OutboundCallDialog({ t }: OutboundCallDialogProps) {
  const [sipHeaders, setSipHeaders] = useState<{id: number, key: string, value: string}[]>([]);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2 px-3 py-1 h-10 text-sm cursor-pointer">
          {/* phone icon and text */}
          {t("phoneNumbersPage.makeAnOutboundCall")}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>{t("phoneNumbersPage.makeOutboundCall")}</DialogTitle>
        <div className="mt-2">
          <Label htmlFor="outbound-phone" className="mb-2 block">{t("phoneNumbersPage.phoneNumber")}</Label>
          <Input id="outbound-phone" placeholder={`${t("common.eg")} +11234567890`} className="mb-4" />
          <div className="flex items-start gap-2 bg-muted/50 rounded-md px-3 py-2 mb-4">
            <span className="mt-1 text-gray-500">
              <Info className="w-4 h-4" />
            </span>
            <span className="text-xs text-gray-600">{t("phoneNumbersPage.internationalCalling")} <span className="underline cursor-pointer">{t("phoneNumbersPage.seeDetails")}</span></span>
          </div>
          <div className="mb-2">
            <div className="font-semibold text-sm mb-1">{t("phoneNumbersPage.customSipHeaders")}</div>
            <div className="text-xs text-gray-500 mb-3">{t("phoneNumbersPage.addKeyValuePairs")}</div>
            {sipHeaders.map((header, idx) => (
              <div className="flex items-center gap-2 mb-3" key={header.id}>
                <Input
                  placeholder={t("phoneNumbersPage.xHeaderName")}
                  className="h-8 text-xs"
                  value={header.key}
                  onChange={e => {
                    const newHeaders = [...sipHeaders];
                    newHeaders[idx].key = e.target.value;
                    setSipHeaders(newHeaders);
                  }}
                />
                <Input
                  placeholder={t("phoneNumbersPage.value")}
                  className="h-8 text-xs"
                  value={header.value}
                  onChange={e => {
                    const newHeaders = [...sipHeaders];
                    newHeaders[idx].value = e.target.value;
                    setSipHeaders(newHeaders);
                  }}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-400 hover:text-red-600 cursor-pointer"
                  onClick={() => setSipHeaders(sipHeaders.filter((_, i) => i !== idx))}
                >
                  <Trash className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              className="flex items-center gap-2 px-3 py-1 h-8 text-sm cursor-pointer"
              onClick={() => setSipHeaders([...sipHeaders, { id: Date.now() + Math.random(), key: '', value: '' }])}
            >
              + {t("common.add")}
            </Button>
          </div>
        </div>
        <DialogFooter className="mt-4">
          <DialogClose asChild>
            <Button variant="outline" className="cursor-pointer">{t("common.cancel")}</Button>
          </DialogClose>
          <Button className="bg-black text-white hover:bg-neutral-800 cursor-pointer">{t("phoneNumbersPage.call")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 