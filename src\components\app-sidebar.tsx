"use client";

import {
  <PERSON>,
  BookOpen,
  Phone,
  PhoneCall,
  History,
  BarChart3,
  CreditCard,
  Key,
  Webhook,
  ChevronDown,
  ChevronLeft,
  Settings,
  HelpCircle,
  LogOut,
  Menu,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { LanguageSwitcher } from "@/components/language-switcher";
import { useAuth } from "@/hooks/use-auth";
import { TenantSelector } from "@/components/auth/tenant-selector";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Link, usePathname } from "@/i18n";
import { routes } from "@/lib/constants/routes";

const data = {
  user: {
    name: "burakgulluler@g...",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "agents" as const,
      url: routes.agents,
      icon: Users,
    },
    {
      title: "knowledgeBase" as const,
      url: routes.knowledgeBase,
      icon: BookOpen,
    },
    {
      title: "phoneNumbers" as const,
      url: routes.phoneNumbers,
      icon: Phone,
    },
    {
      title: "batchCall" as const,
      url: routes.batchCall,
      icon: PhoneCall,
    },
    {
      title: "callHistory" as const,
      url: routes.callHistory,
      icon: History,
    },
    {
      title: "analytics" as const,
      url: routes.analytics,
      icon: BarChart3,
    },
    {
      title: "billing" as const,
      url: routes.billing,
      icon: CreditCard,
    },
    {
      title: "keys" as const,
      url: routes.apiKey,
      icon: Key,
    },
    {
      title: "webhooks" as const,
      url: routes.webhooks,
      icon: Webhook,
    },
  ],
  billing: {
    upcomingInvoice: "$90.20",
    concurrencyUsed: "0/20",
  },
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { state, toggleSidebar } = useSidebar();
  const t = useTranslations();
  const pathname = usePathname();
  const { user, logout } = useAuth();

  return (
    <Sidebar variant="inset" collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            {state === "collapsed" ? (
              <div className="flex items-center justify-center p-3">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={toggleSidebar}
                >
                  <Menu className="h-6 w-6" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2 p-3 relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="flex-shrink-0 h-10 w-10 absolute -right-2 hover:bg-transparent"
                  onClick={toggleSidebar}
                >
                  <ChevronLeft className="h-4 w-4 " />
                </Button>
                <SidebarMenuButton
                  size="lg"
                  className="flex items-center gap-3 flex-1  overflow-visible"
                >
                  <div
                    className="flex   items-center justify-center 
                   hover:scale-105 transition-transform cursor-pointer"
                  >
                    <img
                      src="/ceyber-logo5.png"
                      alt="Logo"
                      className=" h-12 object-contain  mt-2 -ml-2"
                    />
                  </div>
                  {/* <div className="flex flex-col">
                    <span className="font-bold text-xl tracking-tight">
                      {t("app.title")}
                    </span>
                    <span className="text-xs text-muted-foreground font-medium">
                      {t("app.subtitle")}
                    </span>
                  </div> */}
                </SidebarMenuButton>
              </div>
            )}
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <div className="px-3 py-2">
              <TenantSelector />
            </div>
            <SidebarMenu>
              {data.navMain.map((item) => {
                const isActive =
                  pathname === item.url || pathname?.startsWith(item.url + "/");
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      tooltip={t(`sidebar.${item.title}`)}
                      asChild
                      isActive={isActive}
                    >
                      <Link href={item.url}>
                        <item.icon />
                        <span>{t(`sidebar.${item.title}`)}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <div className="space-y-2">
          {state === "expanded" && (
            <div className="px-2">
              <LanguageSwitcher />
            </div>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-start h-auto p-2 hover:bg-accent"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src={data.user.avatar} alt={data.user.name} />
                  <AvatarFallback className="rounded-lg bg-blue-600 text-white">
                    {data.user.name.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {state === "expanded" && (
                  <>
                    <div className="flex flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-medium">
                        {data.user.name}
                      </span>
                    </div>
                    <ChevronDown className="ml-auto size-4" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              side="bottom"
              align="end"
              sideOffset={4}
            >
              <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarFallback className="rounded-lg bg-blue-600 text-white">
                      {user?.name ? user.name.slice(0, 2).toUpperCase() : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user?.name} {user?.surname}
                    </span>
                    <span className="truncate text-xs text-muted-foreground">
                      {user?.email}
                    </span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Settings />
                {t("sidebar.settings")}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <HelpCircle />
                {t("sidebar.helpCenter")}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>
                <LogOut />
                {t("sidebar.logOut")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
