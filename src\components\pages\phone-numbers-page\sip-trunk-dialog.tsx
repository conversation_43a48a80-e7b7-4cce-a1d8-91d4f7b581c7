"use client";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Title, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createInboundTrunkMutationOptions, createOutboundTrunkMutationOptions, deleteTrunkMutationOptions } from "@/query-options/sip-client";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useAuthStore } from "@/store/auth";

interface SipTrunkDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function SipTrunkDialog({ open, setOpen }: SipTrunkDialogProps) {
  const { register, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      phone: '',
      uri: '',
      authUsername: '',
      authPassword: '',
      nickname: ''
    }
  });
  const t = useTranslations();
  const [provider, setProvider] = useState("Inbound");
  const { selectedTenant } = useAuthStore();

  const { mutate: createInboundTrunk } = useMutation(createInboundTrunkMutationOptions);
  const { mutate: createOutboundTrunk } = useMutation(createOutboundTrunkMutationOptions);
  const { mutate: deleteTrunk } = useMutation(deleteTrunkMutationOptions);

  useEffect(() => {
    if (!open) {
      reset();
      setProvider("Inbound");
    }
  }, [open, reset]);

  const onSubmit = (data: any) => {
    const base = {
      name: data.nickname,
      numbers: [data.phone]
    };
    const optionalFields: Record<string, string> = {};
    if (data.authUsername) optionalFields.authUsername = data.authUsername;
    if (data.authPassword) optionalFields.authPassword = data.authPassword;
    if (data.nickname) optionalFields.nickname = data.nickname;
    if (selectedTenant?.key) optionalFields.tenantName = selectedTenant.key;

    if(provider === "Inbound") {
      createInboundTrunk({ ...base, ...optionalFields });
    } else {
      createOutboundTrunk({ ...base, address: data.uri, ...optionalFields });
    }
    reset();
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogTitle>{t("phoneNumbersPage.connectToYourNumberViaSIPTrunking")}</DialogTitle>
        <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-4 space-y-4">
          <div>
            <div className="mb-1 font-medium">{t("phoneNumbersPage.phoneNumber")}</div>
            <Input placeholder={t("phoneNumbersPage.phoneNumberPlaceholder")} {...register("phone")}/>
          </div>
          {provider === "Outbound" && (
            <>
              <div>
                <div className="mb-1 font-medium">{t("phoneNumbersPage.terminationURI")}</div>
                <Input placeholder={t("phoneNumbersPage.terminationURIPlaceholder")} {...register("uri")}/>
              </div>
              <div>
                <div className="mb-1 font-medium">{t("phoneNumbersPage.sipTrunkUserName")} <span className="text-gray-400 font-normal">({t("phoneNumbersPage.optional")})</span></div>
                <Input placeholder={t("phoneNumbersPage.sipTrunkUserNamePlaceholder")} {...register("authUsername")}/>
              </div>
              <div>
                <div className="mb-1 font-medium">{t("phoneNumbersPage.sipTrunkPassword")} <span className="text-gray-400 font-normal">({t("phoneNumbersPage.optional")})</span></div>
                <Input placeholder={t("phoneNumbersPage.sipTrunkPasswordPlaceholder")} {...register("authPassword")}/>
              </div>
            </>
          )}
          <div>
            <div className="mb-1 font-medium">{t("phoneNumbersPage.nickname")} <span className="text-gray-400 font-normal">({t("phoneNumbersPage.optional")})</span></div>
            <Input placeholder={t("phoneNumbersPage.nicknamePlaceholder")} {...register("nickname")}/>
          </div>
          <div className="flex gap-2 mt-4">
            <label htmlFor="sip-inbound-call" className={`flex-1 flex items-center justify-between border rounded-md px-4 py-2 cursor-pointer transition-colors`}>
              <span>{t("phoneNumbersPage.inboundCall")}</span>
              <input
                type="radio"
                id="sip-inbound-call"
                name="sip-call-direction"
                value="inbound"
                checked={provider === "Inbound"}
                onChange={() => setProvider("Inbound")}
                className="ml-4 accent-black"
              />
            </label>
            <label htmlFor="sip-outbound-call" className={`flex-1 flex items-center justify-between border rounded-md px-4 py-2 cursor-pointer transition-colors`}>
              <span>{t("phoneNumbersPage.outboundCall")}</span>
              <input
                type="radio"
                id="sip-outbound-call"
                name="sip-call-direction"
                value="outbound"
                checked={provider === "Outbound"}
                onChange={() => setProvider("Outbound")}
                className="ml-4 accent-black"
              />
            </label>
          </div>
        </div>
        <DialogFooter className="mt-4">
          <DialogClose asChild>
            <Button variant="outline" className="cursor-pointer">{t("common.cancel")}</Button>
          </DialogClose>
          <Button type="submit" className="bg-black text-white hover:bg-neutral-800 cursor-pointer">{t("common.save")}</Button>
        </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 