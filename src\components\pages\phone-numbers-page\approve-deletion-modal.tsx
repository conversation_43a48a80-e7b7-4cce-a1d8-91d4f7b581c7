import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  Di<PERSON>Trigger,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ReactNode, useState } from "react";

interface ApproveDeletionModalProps {
  trigger: ReactNode;
  onApprove: () => void;
}

export default function ApproveDeletionModal({ trigger, onApprove }: ApproveDeletionModalProps) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("phoneNumbersPage.deleteConfirmTitle")}</DialogTitle>
          <DialogDescription>
            {t("phoneNumbersPage.deleteConfirmText")}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button className="cursor-pointer" variant="outline">{t("common.cancel")}</Button>
          </DialogClose>
          <Button
            className="cursor-pointer"
            variant="destructive"
            onClick={() => {
              onApprove();
              setOpen(false);
            }}
          >
            {t("common.delete")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 