import { envs } from "@/lib/constants/envs";
import { SipClient } from "livekit-server-sdk";
import { NextResponse } from "next/server";

export const revalidate = 0;


const dispatchClient = new SipClient(
  envs.livekitUrl,
  envs.livekitApiKey,
  envs.livekitApiSecret
);

export async function GET() {
  try {
    return NextResponse.json(await dispatchClient.listSipInboundTrunk());
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Failed to list SIP trunks" }, { status: 500 });
  }
}

export async function POST(req: Request) {
  const { name, numbers, tenantName } = await req.json();

  try {
    return NextResponse.json(await dispatchClient.createSipInboundTrunk(name, numbers, {
      metadata: tenantName ? `{"tenantName":"${tenantName}"}` : "",
    }));
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Failed to create SIP trunk" }, { status: 500 });
  }
}
