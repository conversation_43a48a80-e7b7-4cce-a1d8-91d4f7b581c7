"use client";

import { useState } from 'react';
import { Plus } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useInboundTrunks, useOutboundTrunks } from '@/hooks/use-sip-trunks';

export const CreateInboundTrunkForm = () => {
  const { createTrunk, isCreating } = useInboundTrunks();
  const [name, setName] = useState('');
  const [numbers, setNumbers] = useState<string[]>([]);
  const [currentNumber, setCurrentNumber] = useState('');

  const addNumber = () => {
    if (currentNumber.trim() && !numbers.includes(currentNumber.trim())) {
      setNumbers([...numbers, currentNumber.trim()]);
      setCurrentNumber('');
    }
  };

  const removeNumber = (numberToRemove: string) => {
    setNumbers(numbers.filter(num => num !== numberToRemove));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim() && numbers.length > 0) {
      createTrunk({ name: name.trim(), numbers });
      setName('');
      setNumbers([]);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create Inbound Trunk</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="inbound-name">Name</Label>
            <Input
              id="inbound-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter trunk name"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="inbound-number">Phone Numbers</Label>
            <div className="flex gap-2">
              <Input
                id="inbound-number"
                value={currentNumber}
                onChange={(e) => setCurrentNumber(e.target.value)}
                placeholder="Enter phone number"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addNumber();
                  }
                }}
              />
              <Button type="button" onClick={addNumber} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {numbers.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {numbers.map((number) => (
                  <Badge
                    key={number}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => removeNumber(number)}
                  >
                    {number} ×
                  </Badge>
                ))}
              </div>
            )}
          </div>
          
          <Button type="submit" disabled={isCreating || !name.trim() || numbers.length === 0}>
            {isCreating ? 'Creating...' : 'Create Inbound Trunk'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export const CreateOutboundTrunkForm = () => {
  const { createTrunk, isCreating } = useOutboundTrunks();
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [numbers, setNumbers] = useState<string[]>([]);
  const [currentNumber, setCurrentNumber] = useState('');
  const [authUsername, setAuthUsername] = useState('');
  const [authPassword, setAuthPassword] = useState('');

  const addNumber = () => {
    if (currentNumber.trim() && !numbers.includes(currentNumber.trim())) {
      setNumbers([...numbers, currentNumber.trim()]);
      setCurrentNumber('');
    }
  };

  const removeNumber = (numberToRemove: string) => {
    setNumbers(numbers.filter(num => num !== numberToRemove));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim() && address.trim() && numbers.length > 0) {
      createTrunk({
        name: name.trim(),
        address: address.trim(),
        numbers,
        authUsername: authUsername.trim() || undefined,
        authPassword: authPassword.trim() || undefined,
      });
      setName('');
      setAddress('');
      setNumbers([]);
      setAuthUsername('');
      setAuthPassword('');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create Outbound Trunk</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="outbound-name">Name</Label>
            <Input
              id="outbound-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter trunk name"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="outbound-address">Address</Label>
            <Input
              id="outbound-address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Enter SIP address"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="outbound-number">Phone Numbers</Label>
            <div className="flex gap-2">
              <Input
                id="outbound-number"
                value={currentNumber}
                onChange={(e) => setCurrentNumber(e.target.value)}
                placeholder="Enter phone number"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addNumber();
                  }
                }}
              />
              <Button type="button" onClick={addNumber} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {numbers.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {numbers.map((number) => (
                  <Badge
                    key={number}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => removeNumber(number)}
                  >
                    {number} ×
                  </Badge>
                ))}
              </div>
            )}
          </div>
          
          <div>
            <Label htmlFor="outbound-username">Auth Username (Optional)</Label>
            <Input
              id="outbound-username"
              value={authUsername}
              onChange={(e) => setAuthUsername(e.target.value)}
              placeholder="Enter auth username"
            />
          </div>
          
          <div>
            <Label htmlFor="outbound-password">Auth Password (Optional)</Label>
            <Input
              id="outbound-password"
              type="password"
              value={authPassword}
              onChange={(e) => setAuthPassword(e.target.value)}
              placeholder="Enter auth password"
            />
          </div>
          
          <Button type="submit" disabled={isCreating || !name.trim() || !address.trim() || numbers.length === 0}>
            {isCreating ? 'Creating...' : 'Create Outbound Trunk'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
