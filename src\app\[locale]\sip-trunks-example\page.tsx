"use client";

import { CreateInboundTrunkForm, CreateOutboundTrunkForm } from '@/components/sip-trunks/create-trunk-form';
import { AllTrunksList } from '@/components/sip-trunks/trunk-list';

export default function SipTrunksExamplePage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">SIP Trunks Management</h1>
        <p className="text-gray-600">
          This page demonstrates the global SIP trunk hooks that cache data and prevent multiple API calls.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        <CreateInboundTrunkForm />
        <CreateOutboundTrunkForm />
      </div>

      <AllTrunksList />
    </div>
  );
}
