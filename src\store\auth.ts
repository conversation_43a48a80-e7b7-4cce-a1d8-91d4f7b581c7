import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: number;
  name: string;
  surname: string;
  username: string;
  email: string;
  personalNumber: string | null;
  validFrom: string;
  validTo: string;
  title: string | null;
  roleGroups: RoleGroup[];
  createdUser: any;
  updatedUser: any;
}

export interface RoleGroup {
  id: number;
  name: string;
  remark: string | null;
  isAdmin: boolean;
  modulesWithPermission: any;
  modules: Module[];
  popUpRightList: any;
  fieldGroupRightList: any;
  actionRightList: ActionRight[];
  menuRightList: any;
}

export interface Module {
  id: number;
  module: string;
}

export interface ActionRight {
  pageId: number;
  name: string;
  permissionType: string;
  areaName: string;
  read: boolean;
  create: boolean;
  update: boolean;
  delete: boolean;
  list: boolean;
  otherActions: string[];
}

export interface Tenant {
  id: number;
  key: string;
}

export interface AuthTokens {
  accessToken: string;
  accessTokenExpiration: string;
  refreshToken: string;
  refreshTokenExpiration: string;
}

interface AuthState {

  user: User | null;
  tokens: AuthTokens | null;
  tenants: Tenant[];
  selectedTenant: Tenant | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  

  setUser: (user: User | null) => void;
  setTokens: (tokens: AuthTokens | null) => void;
  setTenants: (tenants: Tenant[]) => void;
  setSelectedTenant: (tenant: Tenant | null) => void;
  setLoading: (loading: boolean) => void;
  login: (tokens: AuthTokens, user: User) => void;
  logout: () => void;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({

      user: null,
      tokens: null,
      tenants: [],
      selectedTenant: null,
      isAuthenticated: false,
      isLoading: false,


      setUser: (user) => set({ user }),
      
      setTokens: (tokens) => set({ 
        tokens,
        isAuthenticated: !!tokens 
      }),
      
      setTenants: (tenants) => set({ tenants }),
      
      setSelectedTenant: (tenant) => set({ selectedTenant: tenant }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      login: (tokens, user) => set({
        tokens,
        user,
        isAuthenticated: true,
        isLoading: false
      }),
      
      logout: () => set({
        user: null,
        tokens: null,
        tenants: [],
        selectedTenant: null,
        isAuthenticated: false,
        isLoading: false
      }),
      
      clearAuth: () => set({
        user: null,
        tokens: null,
        tenants: [],
        selectedTenant: null,
        isAuthenticated: false,
        isLoading: false
      })
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        tokens: state.tokens,
        user: state.user,
        tenants: state.tenants,
        selectedTenant: state.selectedTenant,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
