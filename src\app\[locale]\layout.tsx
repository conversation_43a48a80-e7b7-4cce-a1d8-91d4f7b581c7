import { Open_Sans } from "next/font/google";
import { notFound } from "next/navigation";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { SidebarWrapper } from "@/components/sidebar-wrapper";
import { SidebarProvider } from "@/components/ui/sidebar";
import { routing } from "@/i18n";

import "../globals.css";
import Providers from "../providers";

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
});

// export const metadata: Metadata = {
//   title: "Create Next App",
//   description: "Generated by create next app",
// };

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  return (
    <html lang={locale}>
      <body className={`${openSans.variable} antialiased`}>
        <Providers>
          <NextIntlClientProvider>
            <SidebarProvider>
              <SidebarWrapper>
                <div className="flex flex-1 flex-col h-screen">
                  <div className="@container/main flex flex-1 flex-col">
                    <div className="flex flex-col h-full">{children}</div>
                  </div>
                </div>
              </SidebarWrapper>
            </SidebarProvider>
          </NextIntlClientProvider>
        </Providers>
      </body>
    </html>
  );
}
