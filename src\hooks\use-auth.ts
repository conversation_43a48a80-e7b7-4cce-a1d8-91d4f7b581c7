import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { authApi, LoginRequest, isTokenExpired, isTokenExpiringSoon } from '@/lib/api/auth';
import { useEffect, useCallback } from 'react';
import { toast } from 'sonner';


export const authQueryKeys = {
  user: ['auth', 'user'] as const,
  tenants: ['auth', 'tenants'] as const,
};

export const useAuth = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const {
    user,
    tokens,
    tenants,
    selectedTenant,
    isAuthenticated,
    isLoading,
    setUser,
    setTokens,
    setTenants,
    setSelectedTenant,
    setLoading,
    login: storeLogin,
    logout: storeLogout,
    clearAuth
  } = useAuthStore();

  useEffect(() => {
    if (!tokens?.accessToken || !tokens?.accessTokenExpiration) return;

    const checkTokenExpiration = () => {
      if (isTokenExpired(tokens.accessTokenExpiration)) {
        handleLogout();
        return;
      }

      if (isTokenExpiringSoon(tokens.accessTokenExpiration)) {
        refreshTokenMutation.mutate(tokens.refreshToken);
      }
    };

    checkTokenExpiration();

    const interval = setInterval(checkTokenExpiration, 60000);

    return () => clearInterval(interval);
  }, [tokens]);

  const loginMutation = useMutation({
    mutationFn: authApi.login,
    onMutate: () => {
      setLoading(true);
    },
    onSuccess: async (tokens) => {
      try {
        const user = await authApi.getCurrentUser(tokens.accessToken);
        
        storeLogin(tokens, user);
        
        const tenants = await authApi.getUserTenants(tokens.accessToken);
        setTenants(tenants);
        
        if (tenants.length > 0) {
          setSelectedTenant(tenants[0]);
        }

        toast.success('Login successful!');
        router.push('/');
      } catch (error) {
        console.error('Post-login setup failed:', error);
        clearAuth();
        toast.error('Login failed. Please try again.');
      }
    },
    onError: (error: Error) => {
      setLoading(false);
      toast.error(error.message || 'Login failed. Please check your credentials.');
    }
  });

  const refreshTokenMutation = useMutation({
    mutationFn: authApi.refreshToken,
    onSuccess: (newTokens) => {
      setTokens(newTokens);
      toast.success('Session refreshed');
    },
    onError: (error) => {
      console.error('Token refresh failed:', error);
      handleLogout();
      toast.error('Session expired. Please login again.');
    }
  });

  const handleLogout = useCallback(async () => {
    try {
      if (tokens?.accessToken) {
        await authApi.logout(tokens.accessToken);
      }
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      storeLogout();
      
      queryClient.clear();
      
      router.push('/auth');
      
      toast.success('Logged out successfully');
    }
  }, [tokens, storeLogout, queryClient, router]);

  const login = useCallback((credentials: LoginRequest) => {
    loginMutation.mutate(credentials);
  }, [loginMutation]);

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user?.roleGroups) return false;
    
    return user.roleGroups.some(roleGroup => 
      roleGroup.actionRightList?.some(action => 
        action.areaName === permission || 
        action.name === permission
      )
    );
  }, [user]);

  const isAdmin = useCallback((): boolean => {
    if (!user?.roleGroups) return false;
    return user.roleGroups.some(roleGroup => roleGroup.isAdmin);
  }, [user]);

  const canAccessModule = useCallback((moduleName: string): boolean => {
    if (!user?.roleGroups) return false;
    
    return user.roleGroups.some(roleGroup => 
      roleGroup.modules?.some(module => 
        module.module.toLowerCase() === moduleName.toLowerCase()
      )
    );
  }, [user]);

  return {
    user,
    tokens,
    tenants,
    selectedTenant,
    isAuthenticated,
    isLoading: isLoading || loginMutation.isPending,
    
    login,
    logout: handleLogout,
    setSelectedTenant,
    
    loginMutation,
    refreshTokenMutation,
    
    hasPermission,
    isAdmin,
    canAccessModule
  };
};

export const useCurrentUser = () => {
  const { tokens } = useAuthStore();
  
  return useQuery({
    queryKey: authQueryKeys.user,
    queryFn: () => authApi.getCurrentUser(tokens!.accessToken),
    enabled: !!tokens?.accessToken,
    staleTime: 5 * 60 * 1000, 
    gcTime: 10 * 60 * 1000, 
  });
};

export const useUserTenants = () => {
  const { tokens } = useAuthStore();
  
  return useQuery({
    queryKey: authQueryKeys.tenants,
    queryFn: () => authApi.getUserTenants(tokens!.accessToken),
    enabled: !!tokens?.accessToken,
    staleTime: 10 * 60 * 1000, 
    gcTime: 30 * 60 * 1000, 
  });
};
