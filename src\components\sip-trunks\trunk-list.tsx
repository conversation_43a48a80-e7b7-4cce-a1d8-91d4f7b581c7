"use client";

import { Loader2, Phone, PhoneCall, RefreshCw } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useInboundTrunks, useOutboundTrunks, useSipTrunks } from '@/hooks/use-sip-trunks';

export const InboundTrunkList = () => {
  const { trunks, isLoading, isError, error, refetch } = useInboundTrunks();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading inbound trunks...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-600 mb-4">
          Error loading inbound trunks: {error?.message}
        </p>
        <Button onClick={() => refetch()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Inbound Trunks</h3>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
      
      {trunks.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Phone className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600">No inbound trunks found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {trunks.map((trunk) => (
            <Card key={trunk.sipTrunkId}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  {trunk.name}
                </CardTitle>
                <CardDescription>ID: {trunk.sipTrunkId}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {trunk.numbers.map((number, index) => (
                    <Badge key={index} variant="secondary">
                      {number}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export const OutboundTrunkList = () => {
  const { trunks, isLoading, isError, error, refetch } = useOutboundTrunks();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading outbound trunks...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-600 mb-4">
          Error loading outbound trunks: {error?.message}
        </p>
        <Button onClick={() => refetch()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Outbound Trunks</h3>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
      
      {trunks.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <PhoneCall className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600">No outbound trunks found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {trunks.map((trunk) => (
            <Card key={trunk.sipTrunkId}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PhoneCall className="h-5 w-5" />
                  {trunk.name}
                </CardTitle>
                <CardDescription>
                  ID: {trunk.sipTrunkId} • Address: {trunk.address}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    {trunk.numbers.map((number, index) => (
                      <Badge key={index} variant="secondary">
                        {number}
                      </Badge>
                    ))}
                  </div>
                  {trunk.authUsername && (
                    <p className="text-sm text-gray-600">
                      Auth Username: {trunk.authUsername}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export const AllTrunksList = () => {
  const { inbound, outbound, isLoading, refetchAll } = useSipTrunks();

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">SIP Trunks</h2>
        <Button onClick={refetchAll} variant="outline" disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh All
        </Button>
      </div>
      
      <div className="grid lg:grid-cols-2 gap-8">
        <div>
          <InboundTrunkList />
        </div>
        <div>
          <OutboundTrunkList />
        </div>
      </div>
    </div>
  );
};
