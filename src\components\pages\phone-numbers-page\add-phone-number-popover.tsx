"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Plus, ArrowLeftRight } from "lucide-react";
import BuyNumberDialog from "@/components/pages/phone-numbers-page/buy-number-dialog";
import SipTrunkDialog from "@/components/pages/phone-numbers-page/sip-trunk-dialog";
import { useTranslations } from "next-intl";

export default function AddPhoneNumberPopover() {
  const t = useTranslations();
  const buyNewNumberLabel = t("phoneNumbersPage.buyNewNumber");
  const connectViaSipLabel = t(
    "phoneNumbersPage.connectToYourNumberViaSIPTrunking"
  );

  const [buyNumberModalOpen, setBuyNumberModalOpen] = useState(false);
  const [sipModalOpen, setSipModalOpen] = useState(false);
  const [popoverOpen, setPopoverOpen] = useState(false);

  return (
    <>
      <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="default"
            className="bg-black hover:bg-neutral-800 text-white font-semibold flex items-center justify-center cursor-pointer ml-2 w-8 h-8 p-0"
            aria-label="Add phone number"
          >
            <Plus className="w-4 h-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="min-w-[320px] max-w-[420px] p-0" align="start">
          <div className="flex flex-col py-2">
            <Button
              variant="ghost"
              size="icon"
              className="flex items-center gap-2 px-4 py-2 hover:bg-gray-50 transition-colors text-left w-full cursor-pointer whitespace-nowrap justify-start"
              onClick={() => setBuyNumberModalOpen(true)}
              disabled={true}
            >
              <Plus className="w-4 h-4 mr-2" />
              <span>{buyNewNumberLabel}</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="flex items-center gap-2 px-4 py-2 hover:bg-gray-50 transition-colors text-left w-full cursor-pointer whitespace-nowrap justify-start"
              onClick={() => {
                setPopoverOpen(false);
                setSipModalOpen(true);
              }}
            >
              <ArrowLeftRight className="w-4 h-4 mr-2" />
              <span>{connectViaSipLabel}</span>
            </Button>
          </div>
        </PopoverContent>
      </Popover>
      <BuyNumberDialog
        open={buyNumberModalOpen}
        setOpen={setBuyNumberModalOpen}
      />
      <SipTrunkDialog
        open={sipModalOpen}
        setOpen={setSipModalOpen}
      />
    </>
  );
}
