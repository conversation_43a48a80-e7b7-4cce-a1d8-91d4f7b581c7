import ky from 'ky';
import { AuthTokens, User, Tenant } from '@/store/auth';

interface ApiResponse<T> {
  data: T;
  errorMessages: string[] | null;
}

const API_BASE_URL = 'https://voiceaiapi.enmdigital.com/api';

export interface LoginRequest {
  email: string;
  password: string;
}

// non-authenticated requests (like login)
const publicApi = ky.create({
  prefixUrl: API_BASE_URL,
  timeout: 30000,
  retry: {
    limit: 2,
    methods: ['get', 'post'],
    statusCodes: [408, 413, 429, 500, 502, 503, 504]
  }
});

// authenticated API client
export const createAuthenticatedApi = (token: string) => {
  return publicApi.extend({
    hooks: {
      beforeRequest: [
        (request) => {
          request.headers.set('Authorization', `Bearer ${token}`);
        }
      ]
    }
  });
};

export const authApi = {
  async login(credentials: LoginRequest): Promise<AuthTokens> {
    try {
      const response = await publicApi.post('Auth/authenticate-user', {
        json: credentials
      }).json<ApiResponse<AuthTokens>>();

      if (response.errorMessages && response.errorMessages.length > 0) {
        throw new Error(response.errorMessages.join(', '));
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Login failed. Please check your credentials.');
    }
  },

  async getCurrentUser(token: string): Promise<User> {
    try {
      const authenticatedApi = createAuthenticatedApi(token);
      const response = await authenticatedApi.get('User/me').json<ApiResponse<User>>();

      if (response.errorMessages && response.errorMessages.length > 0) {
        throw new Error(response.errorMessages.join(', '));
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to fetch user information.');
    }
  },

  async getUserTenants(token: string): Promise<Tenant[]> {
    try {
      const authenticatedApi = createAuthenticatedApi(token);
      const response = await authenticatedApi.get('User/me/tenants').json<ApiResponse<Tenant[]>>();

      if (response.errorMessages && response.errorMessages.length > 0) {
        throw new Error(response.errorMessages.join(', '));
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to fetch user tenants.');
    }
  },

  // placeholder endpoint
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      // TODO: Replace with actual refresh endpoint when available
      const response = await publicApi.post('Auth/refresh-token', {
        json: { refreshToken }
      }).json<ApiResponse<AuthTokens>>();

      if (response.errorMessages && response.errorMessages.length > 0) {
        throw new Error(response.errorMessages.join(', '));
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to refresh token.');
    }
  },

  // if logout endpoint is planned

  // async logout(token: string): Promise<void> {
  //   try {
  //     const authenticatedApi = createAuthenticatedApi(token);
  //     await authenticatedApi.post('Auth/logout');
  //   } catch (error) {
  //     // Logout errors are usually not critical
  //     console.warn('Logout request failed:', error);
  //   }
  // }
};

export const isTokenExpired = (expirationDate: string): boolean => {
  const now = new Date();
  const expiration = new Date(expirationDate);
  return now >= expiration;
};

export const isTokenExpiringSoon = (expirationDate: string): boolean => {
  const now = new Date();
  const expiration = new Date(expirationDate);
  const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
  return fiveMinutesFromNow >= expiration;
};
