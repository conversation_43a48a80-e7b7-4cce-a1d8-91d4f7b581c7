"use client";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { createInboundTrunkQueryOptions, createOutboundTrunkQueryOptions } from "@/query-options/sip-client";
import { useMutation } from "@tanstack/react-query";
import { deleteTrunkMutationOptions } from "@/query-options/sip-client";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import ApproveDeletionModal from "./approve-deletion-modal";
import { useAuthStore } from "@/store/auth";

interface PhoneNumbersTableProps {
  onSelect: (id: string) => void;
}

export default function PhoneNumbersTable({ onSelect }: PhoneNumbersTableProps) {
  const t = useTranslations();
  const { mutate: deleteTrunk } = useMutation(deleteTrunkMutationOptions);
  const { selectedTenant } = useAuthStore();

  const { data: inboundData } = useQuery(createInboundTrunkQueryOptions(selectedTenant?.key || null));
  const { data: outboundData } = useQuery(createOutboundTrunkQueryOptions(selectedTenant?.key || null));
  console.log("inboundData", inboundData);
  console.log("outboundData", outboundData);
  const columns = [
    { key: "id", label: "ID" },
    { key: "phoneNumber", label: t("phoneNumbersPage.phoneNumber") },
    { key: "direction", label: t("phoneNumbersPage.direction") },
    { key: "trunkName", label: t("phoneNumbersPage.trunkName") },
    { key: "actions", label: "" },
  ];

  const allNumbers = [
    ...(inboundData?.flatMap((row: any) =>
      row.numbers.map((number: string) => ({
        number,
        direction: "Inbound Call",
        trunkName: row.name,
        id: row.sipTrunkId,
      }))
    ) || []),
    ...(outboundData?.flatMap((row: any) =>
      row.numbers.map((number: string) => ({
        number,
        direction: "Outbound Call",
        trunkName: row.name,
        id: row.sipTrunkId,
      }))
    ) || []),
  ];

  return (
    <div className="overflow-y-auto rounded bg-white justify-center h-[80vh]">
      <Table className="border-x-0 border-t-0 border-b-0">
        <TableHeader className="sticky top-0 z-20">
          <TableRow className="h-15 bg-gray-50">
            {columns.map((col) => (
              <TableHead key={col.key} className="px-10">{col.label}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {allNumbers.map((row: any) => (
            <TableRow
              key={row.id + row.number}
              className="h-15 py-6 group cursor-pointer"
            >
              <TableCell className="px-10">{row.id}</TableCell>
              <TableCell className="px-10">{row.number}</TableCell>
              <TableCell className="px-10">{row.direction}</TableCell>
              <TableCell className="px-10">{row.trunkName || "null"}</TableCell>
              <TableCell className="px-10">
                <ApproveDeletionModal
                  trigger={
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-red-500 hover:text-red-700 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    >
                      <Trash2 size={18} />
                    </Button>
                  }
                  onApprove={() => deleteTrunk({ sipTrunkId: row.id })}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 