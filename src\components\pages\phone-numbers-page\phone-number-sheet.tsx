import { useState, useRef } from "react";
import { Sheet, SheetContent, SheetTitle } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { PencilLine, Trash, Copy, Check, PhoneCall, Info } from "lucide-react";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogTrigger, DialogContent, DialogTitle, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface PhoneNumberSheetProps {
  selectedRow: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agentList: Array<any>;
  t: any;
}

export default function PhoneNumberSheet({ selectedRow, open, onOpenChange, agentList, t }: PhoneNumberSheetProps) {
  const disableInboundLabel = t("phoneNumbersPage.noneDisableInbound");
  const disableOutboundLabel = t("phoneNumbersPage.noneDisableOutbound");
  const [editing, setEditing] = useState(false);
  const [titleValue, setTitleValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const [copied, setCopied] = useState(false);
  const [webhookChecked, setWebhookChecked] = useState(false);
  const [inboundAgent, setInboundAgent] = useState({ name: disableInboundLabel, version: "" });
  const [outboundAgent, setOutboundAgent] = useState({ name: disableOutboundLabel, version: "" });
  const [inboundPopoverOpen, setInboundPopoverOpen] = useState(false);
  const [outboundPopoverOpen, setOutboundPopoverOpen] = useState(false);
  const [hoveredAgent, setHoveredAgent] = useState<string | null>(null);
  const [hoveredOutboundAgent, setHoveredOutboundAgent] = useState<string | null>(null);

  if (!selectedRow) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="!min-w-0 !max-w-none overflow-y-auto" style={{ width: '35vw', minWidth: 0, maxWidth: 'none', maxHeight: '100vh' }}>
        <SheetTitle className="sr-only">Phone number details</SheetTitle>
        <div className="h-5" />
        <div className="w-full border-b border-gray-200 mt-4" />
        <div className="">
          <div className="flex items-center justify-between gap-4 mb-2 px-3">
            <div className="flex items-center gap-2">
              {editing ? (
                <Input
                  ref={inputRef}
                  className="text-lg font-medium w-64"
                  value={titleValue}
                  onChange={e => setTitleValue(e.target.value)}
                  onBlur={() => setEditing(false)}
                  onKeyDown={e => {
                    if (e.key === "Enter") setEditing(false);
                  }}
                  autoFocus
                />
              ) : (
                <span className="text-lg font-medium">
                  {selectedRow.phoneNumber} <span>(oprisuseu) Puhax</span>
                </span>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="ml-1 text-gray-400 hover:text-gray-600 p-1 rounded cursor-pointer"
                tabIndex={0}
                type="button"
                onClick={() => {
                  setTitleValue(`${selectedRow.phoneNumber} (oprisuseu) Puhax`);
                  setEditing(true);
                  setTimeout(() => inputRef.current?.focus(), 0);
                }}
              >
                <PencilLine className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center justify-center p-2 h-8 w-8 cursor-pointer">
                <Trash className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
} 