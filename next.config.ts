import createNextIntlPlugin from "next-intl/plugin";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: "standalone",
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    domains: ["picsum.photos"],
  },
  env: {
    NEXT_PUBLIC_APP_URL: "http://localhost:3000",
    LIVEKIT_URL: "wss://livekit3.enmdigital.com",
    LIVEKIT_API_KEY: "devkey",
    LIVEKIT_API_SECRET: "secret",
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
