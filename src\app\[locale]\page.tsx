"use client";

import { useEffect } from "react";
import { useRouter } from "@/i18n";
import { ProtectedRoute } from "@/components/auth/protected-route";

const HomePage = () => {
  const router = useRouter();

  useEffect(() => {
    router.push("/agents");
  }, [router]);

  return null;
};

const ProtectedHomePage = () => {
  return (
    <ProtectedRoute>
      <HomePage />
    </ProtectedRoute>
  );
};

export default ProtectedHomePage;
