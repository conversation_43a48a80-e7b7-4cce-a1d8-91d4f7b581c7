

### ilgili hook'a src\hooks\use-sip-trunks.ts 'dan ulaşabilirsin

### örnek adresi: http://localhost:3000/sip-trunks-example

### `useInboundTrunks()`

```tsx
import { useInboundTrunks } from '@/hooks/use-sip-trunks';

function MyComponent() {
  const { 
    trunks,        // SipInboundTrunk[]
    isLoading,     // boolean
    isError,       // boolean
    error,         // Error | null
    refetch,       // () => void
    createTrunk,   // (data: CreateInboundTrunkRequest) => void
    isCreating     // boolean
  } = useInboundTrunks();

  return (
    <div>
      {isLoading && <p>Loading...</p>}
      {isError && <p>Error: {error?.message}</p>}
      {trunks.map(trunk => (
        <div key={trunk.sipTrunkId}>{trunk.name}</div>
      ))}
    </div>
  );
}
```

### `useOutboundTrunks()`

```tsx
import { useOutboundTrunks } from '@/hooks/use-sip-trunks';

function MyComponent() {
  const { 
    trunks,        // SipOutboundTrunk[]
    isLoading,     // boolean
    isError,       // boolean
    error,         // Error | null
    refetch,       // () => void
    createTrunk,   // (data: CreateOutboundTrunkRequest) => void
    isCreating     // boolean
  } = useOutboundTrunks();

  // Usage is the same as useInboundTrunks
}
```

### `useSipTrunks()`


```tsx
import { useSipTrunks } from '@/hooks/use-sip-trunks';

function MyComponent() {
  const { 
    inbound,       // Same as useInboundTrunks() return
    outbound,      // Same as useOutboundTrunks() return
    isLoading,     // boolean (true if either is loading)
    refetchAll     // () => void (refetches both)
  } = useSipTrunks();

  return (
    <div>
      <h2>Inbound Trunks: {inbound.trunks.length}</h2>
      <h2>Outbound Trunks: {outbound.trunks.length}</h2>
    </div>
  );
}
```

## Creating New Trunks

### Inbound Trunk

```tsx
const { createTrunk, isCreating } = useInboundTrunks();

const handleCreate = () => {
  createTrunk({
    name: "My Inbound Trunk",
    numbers: ["+1234567890", "+0987654321"]
  });
};
```

### Outbound Trunk

```tsx
const { createTrunk, isCreating } = useOutboundTrunks();

const handleCreate = () => {
  createTrunk({
    name: "My Outbound Trunk",
    address: "sip.example.com",
    numbers: ["+1234567890"],
    authUsername: "user",
    authPassword: "pass"
  });
};
```
