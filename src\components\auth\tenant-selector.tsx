"use client";

import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Building2 } from 'lucide-react';

export const TenantSelector = () => {
  const { tenants, selectedTenant, setSelectedTenant } = useAuth();

  if (!tenants || tenants.length === 0) {
    return null;
  }

  if (tenants.length === 1) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600">
        <Building2 className="h-4 w-4" />
        <span>{tenants[0].key}</span>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="justify-between">
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span>{selectedTenant?.key || 'Select Tenant'}</span>
          </div>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56">
        <DropdownMenuLabel>Switch Tenant</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {tenants.map((tenant) => (
          <DropdownMenuItem
            key={tenant.id}
            onClick={() => setSelectedTenant(tenant)}
            className={selectedTenant?.id === tenant.id ? 'bg-accent' : ''}
          >
            <Building2 className="mr-2 h-4 w-4" />
            <span>{tenant.key}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
