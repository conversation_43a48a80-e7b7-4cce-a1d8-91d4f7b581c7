import { mutationOptions, queryOptions } from "@tanstack/react-query";
import ky from "ky";
import { envs } from "@/lib/constants/envs";
import { getQueryClient } from "@/app/providers";
import { endpoints } from "@/lib/constants/endpoints";

interface SipTrunkData {
  sipTrunkId: string;
  name: string;
  metadata: string;
  address: string;
  transport: string;
  numbers: string[];
  authUsername: string;
  authPassword: string;
  headers: object;
  headersToAttributes: object;
  attributesToHeaders: object;
  includeHeaders: string;
  mediaEncryption: string;
  destinationCountry: string;
}

const filterTrunksByTenant = (trunks: SipTrunkData[], selectedTenantKey: string | null) => {
  if (!selectedTenantKey) return trunks;

  return trunks.filter(trunk => {
    if (!trunk.metadata) return false;

    try {
      const metadata = JSON.parse(trunk.metadata);
      return metadata.tenantName === selectedTenantKey;
    } catch {
      return false;
    }
  });
};

export const createInboundTrunkQueryOptions = (selectedTenantKey: string | null) => queryOptions({
  queryKey: ["listSipInboundTrunk", selectedTenantKey],
  queryFn: async () => {
    console.log("Fetching inbound trunks from:", `${envs.appUrl}${endpoints.inboundTrunk}`);
    const allTrunks = await ky.get(`${envs.appUrl}${endpoints.inboundTrunk}`).json<SipTrunkData[]>();

    return filterTrunksByTenant(allTrunks, selectedTenantKey);
  },
});

// Legacy export for backward compatibility
export const inboundTrunkQueryOptions = createInboundTrunkQueryOptions(null);

export const createOutboundTrunkQueryOptions = (selectedTenantKey: string | null) => queryOptions({
  queryKey: ["listSipOutboundTrunk", selectedTenantKey],
  queryFn: async () => {
    const allTrunks = await ky.get(`${envs.appUrl}${endpoints.outboundTrunk}`).json<SipTrunkData[]>();

    return filterTrunksByTenant(allTrunks, selectedTenantKey);
  },
});

// Legacy export for backward compatibility
export const outboundTrunkQueryOptions = createOutboundTrunkQueryOptions(null);

export const createInboundTrunkMutationOptions = mutationOptions({
  mutationFn: async (data: { name: string; numbers: string[]; authUsername?: string; authPassword?: string; tenantName?: string }) => {
    return await ky.post(`${envs.appUrl}${endpoints.inboundTrunk}`, { json: data }).json();
  },
  onSuccess: () => {
    getQueryClient().invalidateQueries({ queryKey: ["listSipInboundTrunk"] });
  },
});

export const createOutboundTrunkMutationOptions = mutationOptions({
  mutationFn: async (data: { name: string; numbers: string[]; address: string; authUsername?: string; authPassword?: string; tenantName?: string }) => {
    return await ky.post(`${envs.appUrl}${endpoints.outboundTrunk}`, { json: data }).json();
  },
  onSuccess: () => {
    getQueryClient().invalidateQueries({ queryKey: ["listSipOutboundTrunk"] });
  },
});

export const deleteTrunkMutationOptions = mutationOptions({
  mutationFn: async (data: { sipTrunkId: string }) => {
    return await ky.delete(`${envs.appUrl}${endpoints.trunk}/${data.sipTrunkId}`).json();
  },
  onSuccess: () => {
    const queryClient = getQueryClient();
    queryClient.invalidateQueries({ queryKey: ["listSipInboundTrunk"] });
    queryClient.invalidateQueries({ queryKey: ["listSipOutboundTrunk"] });
  },
});