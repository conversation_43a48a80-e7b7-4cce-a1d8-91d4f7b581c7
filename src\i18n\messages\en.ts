const en = {
  title: "en title",
  common: {
    cancel: "Cancel",
    apply: "Apply",
    save: "Save",
    previous: "Previous",
    next: "Next",
    showMore: "Show more",
    showLess: "Show less",
    add: "Add",
    delete: "Delete",
    eg: "e.g.",
    learnMore: "Learn more",
    current: "Current",
  },
  sidebar: {
    agents: "Agents",
    knowledgeBase: "Knowledge Base",
    phoneNumbers: "Phone Numbers",
    batchCall: "Batch Call",
    callHistory: "Call History",
    analytics: "Analytics",
    billing: "Billing",
    keys: "API Keys",
    webhooks: "Webhooks",
    settings: "Settings",
    helpCenter: "Help Center",
    logOut: "Log out",
    toggleSidebar: "Toggle Sidebar",
  },
  phoneNumbersPage: {
    title: "Phone Numbers",
    buyNewNumber: "Buy New Number",
    buyPhoneNumber: "Buy Phone Number",
    phoneNumber: "Phone Number",
    direction: "Direction",
    trunkName: "Trunk Name",
    provider: "Provider",
    areaCode: "Area Code",
    areaCodePlaceholder: "e.g. 650",
    optional: "Optional",
    monthlyFee: "This number incurs a monthly fee of",
    connectToYourNumberViaSIPTrunking: "Connect to your number via SIP trunking",
    phoneNumberPlaceholder: "Enter phone number",
    terminationURI: "Termination URI",
    terminationURIPlaceholder: "Enter termination URI (NOT SIP server uri)",
    sipTrunkUserName: "SIP Trunk User Name",
    sipTrunkUserNamePlaceholder: "Enter SIP Trunk User Name",
    sipTrunkPassword: "SIP Trunk Password",
    sipTrunkPasswordPlaceholder: "Enter SIP Trunk Password",
    nickname: "Nickname",
    nicknamePlaceholder: "Enter Nickname",
    inboundCall: "Inbound Call",
    outboundCall: "Outbound Call",
    deleteConfirmTitle: "Are you sure you want to delete this trunk?",
    deleteConfirmText: "This action cannot be undone.",
    inboundCallAgent: "Inbound Call Agent",
    outboundCallAgent: "Outbound Call Agent",
    makeAnOutboundCall: "Make an outbound call",
    makeOutboundCall: "Make Outbound Call",
    internationalCalling: "If you want international calling, please use a custom telephony provider",
    seeDetails: "See Details",
    customSipHeaders: "Custom SIP Headers",
    addKeyValuePairs: "Add key/value pairs for call routing, metadata, or carrier integration.",
    xHeaderName: "X-Header Name",
    value: "Value",
    call: "Call",
    noneDisableInbound: "None (disable inbound)",
    noneDisableOutbound: "None (disable outbound)",
    addAnInboundWebhook: "Add an inbound webhook",
    enterURL: "Enter url",
  },
  callHistoryPage: {
    title: "Call History",
    dateRange: "Date Range",
    filter: "Filter",
    filterByAgent: "Filter by Agent",
    agent: "Agent",
    noAgentFound: "No agent found",
    agentsSelected: "agents selected",
    filterByCallId: "Filter by Call ID",
    enterCallId: "Enter Call ID",
    customizeField: "Customize Field",
    export: "Export",
    time: "Time",
    duration: "Duration",
    channelType: "Channel Type",
    cost: "Cost",
    sessionId: "Session ID",
    endSession: "End Session",
    sessionStatus: "Session Status",
    userSentiment: "User Sentiment",
    agentId: "Agent ID",
    agentVersion: "Agent Version",
    agentName: "Agent Name",
    from: "From",
    to: "To",
    sessionOutcome: "Session Outcome",
    endToEndLatency: "End to End Latency",
    searchAgent: "Search agent...",
    phoneCall: "Phone Call",
    webCall: "Web Call",
    conversationAnalysis: "Conversation Analysis",
    preset: "Preset",
    callSuccessful: "Call Successful",
    successful: "Successful",
    unsuccessful: "Unsuccessful",
    callStatus: "Call Status",
    disconnectionReason: "Disconnection Reason",
    summary: "Summary",
    transcription: "Transcription",
    noTranscription: "No Transcription",
    data: "Data",
    noDynamicVariables: "No Dynamic Variables",
    noExtractedDynamicVariables: "No Extracted Dynamic Variables",
    detailLogs: "Detail Logs",
  },
  app: {
    title: "Voice AI",
    subtitle: "Smart Conversations",
    language: "Language",
    selectLanguage: "Select Language",
  },
  AgentConf: {
    accordionItems: {
      functions: {
        title: "Functions",
        description:
          "Enable your agent with capabilities such as calendar bookings, call termination, etc.",
      },
      knowledgeBase: {
        title: "Knowledge Base",
        description: "Add knowledge base to provide context to the agent.",
      },
      speech: {
        title: "Speech Settings",
        backgroundSound: "Background Sound",
        backgroundSoundVolume: "Background Sound Volume",
        responsiveness: {
          title: "Responsiveness",
          description:
            "Control how fast the agent responds after users finish speaking.",
          slowTooltip: "When tuned down, the latency will increase",
        },
        interruption: {
          title: "Interruption Sensitivity",
          description:
            "Control how sensitively AI can be interrupted by human speech.",
        },
        backchanneling: {
          title: "Enable Backchanneling",
          description:
            "Enables the agent to use affirmations like 'yeah' or 'uh-huh' during conversations, indicating active listening and engagement.",
        },
        normalization: {
          title: "Enable Speech Normalization",
          description:
            "It converts text elements like numbers, currency, and dates into human-like spoken forms. (Learn more)",
          slowTooltip: "This setting will incur 100ms latency",
        },
        reminder: {
          title: "Reminder Message Frequency",
          description: "Control how often AI will send a reminder message.",
        },
        pronunciation: {
          title: "Pronunciation",
          description:
            "Guide the model to pronunce a word, name, or phrase in a specific way. (Learn more)",
        },
        sounds: {
          coffeeShop: "Coffee Shop",
          conventionHall: "Convention Hall",
          summerOutdoor: "Summer Outdoor",
          mountainOutdoor: "Mountain Outdoor",
          staticNoise: "Static Noise",
          callCenter: "Call Center",
        },
      },
      transcription: {
        title: "Transcription Settings",
        denoising: {
          title: "Denoising Mode",
          description:
            "Filter out unwanted background noise or speech. (Learn more)",
          removeNoise: "Remove noise",
          removeNoiseAndSpeech: "Remove noise + background speech",
          tooltip:
            "Removes background speech such as human voices from a TV with aggressive denoising, may reduce transcription accuracy in certain scenarios",
        },
        mode: {
          title: "Transcription Mode",
          description: "Balance between speed and accuracy. (Learn more)",
          speed: "Optimize for speed",
          accuracy: "Optimize for accuracy",
          latencyWarning: "This setting will sometimes incur 200ms latency",
          accuracyInfo:
            "Performs better with numbers and dates by preserving longer context and avoiding speech cutoffs.",
        },
        keywords: {
          title: "Boosted Keywords",
          description:
            "Provide a customized list of keywords to expand our models' vocabulary.",
        },
      },
      call: {
        title: "Call Settings",
        voicemail: {
          title: "Voicemail Detection",
          description:
            "Hang up or leave a voicemail if a voicemail is detected.",
        },
        keypad: {
          title: "User Keypad Input Detection",
          description:
            "Enable the AI to listen for keypad input during a call.",
        },
        conditions: {
          description:
            "The AI will respond when any of the following conditions are met:",
        },
        timeout: {
          title: "Timeout",
          description:
            "The AI will respond if no keypad input is detected within the set time.",
        },
        terminationKey: {
          title: "Termination Key",
          description:
            "The AI will respond when the user presses the configured termination key (0-9, #, *).",
        },
        digitLimit: {
          title: "Digit Limit",
          description:
            "The AI will respond immediately after the caller enters the configured number of digits.",
        },
        silence: {
          title: "End Call on Silence",
          description:
            "End the call if user stays silent for extended period of time.",
        },
        maxDuration: {
          title: "Max Call Duration",
        },
        pauseBeforeSpeaking: {
          title: "Pause Before Speaking",
          description:
            "The duration before the assistant starts speaking at the beginning of the call.",
        },
        ringDuration: {
          title: "Ring Duration",
          description:
            "The max ringing duration before the outbound call / transfer call is deemed no answer.",
        },
      },
      analysis: {
        title: "Post-Call Analysis",
        dataRetrieval: {
          title: "Post Call Data Retrieval",
          description:
            "Define the information that you need to extract from the voice. (Learn more)",
        },
      },
      security: {
        title: "Security & Fallback Settings",
        optOut: {
          title: "Opt Out Sensitive Data Storage",
          description:
            "Control whether Retell should store sensitive data. (Learn more)",
        },
        optInUrls: {
          title: "Opt In Secure URLs",
          description:
            "Add security signatures to URLs. The URLs expire after 24 hours. (Learn more)",
        },
        fallbackVoice: {
          title: "Fallback Voice ID",
          description:
            "If the current voice provider fails, assign a fallback voice to continue the call.",
        },
        defaultVars: {
          title: "Default Dynamic Variables",
          description:
            "Set fallback values for dynamic variables across all endpoints if they are not provided.",
        },
      },
      webhook: {
        title: "Webhook Settings",
        urlTitle: "Agent Level Webhook URL",
        urlDescription: "Webhook URL to receive events. (Learn more)",
      },
    },
    common: {
      add: "Add",
      setUp: "Set up",
      none: "None",
      seconds: "seconds",
      times: "times",
      shortSecond: "s",
      shortMinute: "m",
      shortHour: "h",
    },
  },
  analyticsPage: {
    title: "Analytics",
    timeRanges: {
      today: "Today",
      lastWeek: "Last 1 week",
      last4Weeks: "Last 4 weeks",
      last3Months: "Last 3 months",
      weekToDate: "Week to date",
      monthToDate: "Month to date",
      yearToDate: "Year to date",
      allTime: "All time",
      customRange: "Custom range",
    },
    filter: "Filter",
    add: "Add",
    edit: "Edit",
    search: "Search...",
    allAgents: "All agents",
    range: "Range",
    metrics: {
      callCounts: "Call Counts",
      callDuration: "Call Duration",
      callLatency: "Call Latency",
      callSuccessful: "Call Successful",
      disconnectionReason: "Disconnection Reason",
      userSentiment: "User Sentiment",
      phoneInboundOutbound: "Phone inbound/outbound",
      callPickedUpRate: "Call Picked Up Rate",
      callSuccessfulRate: "Call Successful Rate",
      callTransferRate: "Call Transfer Rate",
      voicemailRate: "Voicemail Rate",
      averageCallDuration: "Average call duration",
      averageLatency: "Average Latency",
    },
    chartLabels: {
      successful: "Successful",
      unknown: "Unknown",
      unsuccessful: "Unsuccessful",
      inbound: "Inbound",
      outbound: "Outbound",
      negative: "Negative",
      positive: "Positive",
      neutral: "Neutral",
      callCounts: "Call counts",
      callPickupRate: "Call Pickup Rate",
      callSuccessRate: "Call Success Rate",
      callTransferRate: "Call Transfer Rate",
      voicemailRate: "Voicemail rate",
      callDuration: "Call duration",
      endToEndLatency: "End to end latency",
      callCountsSuccessful: "Call counts: successful",
      callCountsUnknown: "Call counts: unknown",
      callCountsUnsuccessful: "Call counts: unsuccessful",
    },
    disconnectionReasons: {
      dialNoAnswer: "Dial no answer",
      agentHangup: "Agent hangup",
      callTransfer: "Call transfer",
      userHangup: "User hangup",
      systemError: "System error",
      networkIssue: "Network issue",
      timeout: "Timeout",
    },
    allLegendItems: "More",
    deleteConfirmTitle: "Are you sure you want to delete this chart?",
    deleteConfirmText:
      "This action cannot be undone. The chart will be permanently removed.",
  },
  Auth: {
    login: "Login",
    register: "Register",
    forgotPassword: "Forgot password?",
    backToLogin: "← Back to Login",
    email: "Email",
    password: "Password",
    name: "Name",
    resetTitle: "Reset Password",
    resetButton: "Send Reset Link",
    nameError: "Name must be at least 2 characters",
    emailError: "Please enter a valid email address",
  },
  PasswordInput: {
    password: "Password",
    showPassword: "Show password",
    hidePassword: "Hide password",
    passwordChecklist: {
      length: "At least 6 characters",
      uppercase: "At least one uppercase letter",
      number: "At least one number",
      special: "At least one special character",
    },
  },
};

export default en;
