"use client";

import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { authApi, isTokenExpired } from '@/lib/api/auth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { tokens, user, clearAuth, setUser, setTenants } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      if (!tokens?.accessToken) {
        return;
      }

      if (isTokenExpired(tokens.accessTokenExpiration)) {
        clearAuth();
        return;
      }

      if (!user) {
        try {
          const userData = await authApi.getCurrentUser(tokens.accessToken);
          setUser(userData);

          const tenants = await authApi.getUserTenants(tokens.accessToken);
          setTenants(tenants);
        } catch (error) {
          console.error('Failed to fetch user data:', error);
          clearAuth();
        }
      }
    };

    initializeAuth();
  }, [tokens, user, clearAuth, setUser, setTenants]);

  return <>{children}</>;
};
