import ky from 'ky';
import { useAuthStore } from '@/store/auth';

const API_BASE_URL = 'https://voiceaiapi.enmdigital.com/api';

export const apiClient = ky.create({
  prefixUrl: API_BASE_URL,
  timeout: 30000,
  retry: {
    limit: 2,
    methods: ['get', 'post', 'put', 'delete'],
    statusCodes: [408, 413, 429, 500, 502, 503, 504]
  },
  hooks: {
    beforeRequest: [
      async (request) => {
        const { tokens, clearAuth, selectedTenant } = useAuthStore.getState();

        if (tokens?.accessToken) {
          const now = new Date();
          const expiration = new Date(tokens.accessTokenExpiration);

          if (now >= expiration) {
            clearAuth();
            throw new Error('Token expired');
          }

          request.headers.set('Authorization', `Bearer ${tokens.accessToken}`);
        }
        if (selectedTenant?.key) {
          request.headers.set("tenantName", selectedTenant.key);
        }
      },
    ],
    afterResponse: [
      async (_request, _options, response) => {
        if (response.status === 401) {
          const { clearAuth } = useAuthStore.getState();
          clearAuth();
          throw new Error('Unauthorized');
        }

        return response;
      }
    ]
  }
});

export interface ApiResponse<T> {
  data: T;
  errorMessages: string[] | null;
}

export const handleApiResponse = async <T>(response: Response): Promise<T> => {
  const data: ApiResponse<T> = await response.json();
  
  if (data.errorMessages && data.errorMessages.length > 0) {
    throw new Error(data.errorMessages.join(', '));
  }
  
  return data.data;
};

export const createAuthenticatedClient = () => {
  return apiClient;
};
