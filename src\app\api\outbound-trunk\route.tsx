import { envs } from "@/lib/constants/envs";
import { CreateSipOutboundTrunkOptions, SipClient } from "livekit-server-sdk";
import { NextResponse } from "next/server";

export const revalidate = 0;

const dispatchClient = new SipClient(
  envs.livekitUrl,
  envs.livekitApiKey,
  envs.livekitApiSecret
);

export async function GET() {
  try {
    return NextResponse.json(await dispatchClient.listSipOutboundTrunk());
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Failed to list SIP trunks" }, { status: 500 });
  }
}

export async function POST(req: Request) {
  const { name, numbers, address, authUsername, authPassword, tenantName } = await req.json();
  try {
    return NextResponse.json(await dispatchClient.createSipOutboundTrunk(name, address, numbers, {
      authUsername: authUsername,
      authPassword: authPassword,
      metadata: tenantName ? `{"tenantName":"${tenantName}"}` : "",
    } as CreateSipOutboundTrunkOptions));
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: "Failed to create SIP trunk" }, { status: 500 });
  }
}
