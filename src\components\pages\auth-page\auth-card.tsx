"use client";

import { useTranslations } from "next-intl";
import { useState } from "react";
import ForgotPasswordForm from "@/components/pages/auth-page/forgot-password-form";
import LoginForm from "@/components/pages/auth-page/login-form";
import RegisterForm from "@/components/pages/auth-page/register-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const AuthCard = () => {
  const [form, setForm] = useState<"login" | "forgot">("login");
  const t = useTranslations();

  return (
    <Card className="w-full max-w-md">
      <CardContent className="p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold">{t("Auth.login")}</h1>
          <p className="text-gray-600 mt-2">Welcome back! Please sign in to your account.</p>
        </div>

        {form === "login" && <LoginForm onForgot={() => setForm("forgot")} />}
        {form === "forgot" && (
          <ForgotPasswordForm onBack={() => setForm("login")} />
        )}
      </CardContent>
    </Card>
  );
};

export default AuthCard;
