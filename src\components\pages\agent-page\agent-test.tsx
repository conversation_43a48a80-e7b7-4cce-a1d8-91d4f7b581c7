"use client";

import { Mic } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { PhoneInput } from "@/components/ui/phone-input";
import { useAgentConfigStore } from "@/store/agent-config";
import { Checkbox } from "@/components/ui/checkbox";


const AgentTest = () => {
  const [phonenumber, setPhonenumber] = useState<string>("");
  const { config } = useAgentConfigStore();
  const [includePlus, setIncludePlus] = useState<boolean>(false);

  return (
    <div className="h-full flex items-center justify-center">
      <div className="flex gap-2 flex-col items-center">
        <Mic size={64} />
        <PhoneInput
          onChange={(value) => {
            setPhonenumber(value);
          }}
          defaultCountry="TR"
          countries={["TR"]}
        />
        <div className="mt-3 flex items-center space-x-2">
          <Checkbox
            id="compare-previous"
            checked={includePlus}
            onCheckedChange={(checked) =>
              setIncludePlus(checked === true)
            }
          />
          <label
            htmlFor="compare-previous"
            className="text-sm text-muted-foreground"
          >
            + Dahil edilsin mi?
          </label>
        </div>
        <Button
          onClick={() => {
            fetch("/api/start-call", {
              method: "POST",
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                metadata: {
                  ...config,
                },
                outbound_trunk_id: "ST_EzjnhUfVcTzg",
                phonenumber: includePlus === true ? "+" + phonenumber.replace("+", "") : phonenumber.replace("+", "")
              }),
            });
          }}
          className="w-full"
        >
          Test your agent
        </Button>
      </div>
    </div>
  );
};

export default AgentTest;
