"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { FormControl } from "@/components/form-control";
import PasswordInput from "@/components/password-input";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

export default function LoginForm({ onForgot }: { onForgot: () => void }) {
  const t = useTranslations();
  const { login, isLoading } = useAuth();

  const loginSchema = z.object({
    email: z.string().email({ message: t("Auth.emailError") }),
    password: z
      .string()
      .min(1, { message: "Password is required" }),
      //    .min(6, { message: t("PasswordInput.passwordChecklist.length") })
      // .regex(/[A-Z]/, {
      //   message: t("PasswordInput.passwordChecklist.uppercase"),
      // })
      // .regex(/\d/, { message: t("PasswordInput.passwordChecklist.number") })
      // .regex(/[!@#$%^&*(),.?":{}|<>]/, {
      //   message: t("PasswordInput.passwordChecklist.special"),
      // }),
  });

  type LoginFormValues = z.infer<typeof loginSchema>;
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = (data: LoginFormValues) => {
    login(data);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <FormControl
        label={t("Auth.email")}
        id="email"
        error={form.formState.errors.email?.message}
        isRequired
      >
        <Input type="email" {...form.register("email")} />
      </FormControl>

      <FormControl label={t("Auth.password")} id="login-password" isRequired>
        <PasswordInput {...form.register("password")} />
      </FormControl>

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {t("Auth.login")}
      </Button>

      <div className="text-right text-sm">
        <button
          type="button"
          onClick={onForgot}
          className="text-blue-600 hover:underline"
        >
          {t("Auth.forgotPassword")}
        </button>
      </div>
    </form>
  );
}
