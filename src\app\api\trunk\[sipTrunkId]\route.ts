import { envs } from "@/lib/constants/envs";
import { SipClient } from "livekit-server-sdk";
import { NextResponse } from "next/server";

export const revalidate = 0;

const dispatchClient = new SipClient(
  envs.livekitUrl,
  envs.livekitApiKey,
  envs.livekitApiSecret
);

export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ sipTrunkId: string }> }
) {
  const { sipTrunkId } = await params;

  try {
    const result = await dispatchClient.deleteSipTrunk(sipTrunkId);
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Delete failed:", error);
    return NextResponse.json(
      { error: "Failed to delete SIP trunk" },
      { status: 500 }
    );
  }
}
