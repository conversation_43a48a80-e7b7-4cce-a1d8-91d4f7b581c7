"use client";

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useInboundTrunks, useOutboundTrunks, useSipTrunks } from '@/hooks/use-sip-trunks';

// Example 1: Simple trunk counter component
export const TrunkCounter = () => {
  const { inbound, outbound } = useSipTrunks();
  
  return (
    <div className="flex gap-4">
      <Badge variant="secondary">
        Inbound: {inbound.trunks.length}
      </Badge>
      <Badge variant="secondary">
        Outbound: {outbound.trunks.length}
      </Badge>
    </div>
  );
};

// Example 2: Simple inbound trunk dropdown/selector
export const InboundTrunkSelector = ({ 
  onSelect 
}: { 
  onSelect: (trunkId: string) => void 
}) => {
  const { trunks, isLoading } = useInboundTrunks();
  
  if (isLoading) return <div>Loading trunks...</div>;
  
  return (
    <select onChange={(e) => onSelect(e.target.value)} className="border rounded p-2">
      <option value="">Select an inbound trunk</option>
      {trunks.map(trunk => (
        <option key={trunk.sipTrunkId} value={trunk.sipTrunkId}>
          {trunk.name} ({trunk.numbers.length} numbers)
        </option>
      ))}
    </select>
  );
};

// Example 3: Simple outbound trunk selector
export const OutboundTrunkSelector = ({ 
  onSelect 
}: { 
  onSelect: (trunkId: string) => void 
}) => {
  const { trunks, isLoading } = useOutboundTrunks();
  
  if (isLoading) return <div>Loading trunks...</div>;
  
  return (
    <select onChange={(e) => onSelect(e.target.value)} className="border rounded p-2">
      <option value="">Select an outbound trunk</option>
      {trunks.map(trunk => (
        <option key={trunk.sipTrunkId} value={trunk.sipTrunkId}>
          {trunk.name} ({trunk.address})
        </option>
      ))}
    </select>
  );
};

// Example 4: Trunk status dashboard
export const TrunkStatusDashboard = () => {
  const { inbound, outbound, isLoading, refetchAll } = useSipTrunks();
  
  const totalNumbers = inbound.trunks.reduce((sum, trunk) => sum + trunk.numbers.length, 0) +
                      outbound.trunks.reduce((sum, trunk) => sum + trunk.numbers.length, 0);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          SIP Trunk Status
          <Button onClick={refetchAll} variant="outline" size="sm" disabled={isLoading}>
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold">{inbound.trunks.length}</div>
            <div className="text-sm text-gray-600">Inbound Trunks</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{outbound.trunks.length}</div>
            <div className="text-sm text-gray-600">Outbound Trunks</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{totalNumbers}</div>
            <div className="text-sm text-gray-600">Total Numbers</div>
          </div>
          <div>
            <div className="text-2xl font-bold">
              {inbound.trunks.length + outbound.trunks.length}
            </div>
            <div className="text-sm text-gray-600">Total Trunks</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Example 5: Quick trunk info component (can be used anywhere)
export const QuickTrunkInfo = () => {
  const { trunks: inboundTrunks } = useInboundTrunks();
  const { trunks: outboundTrunks } = useOutboundTrunks();
  
  return (
    <div className="text-sm text-gray-600">
      {inboundTrunks.length} inbound, {outboundTrunks.length} outbound trunks configured
    </div>
  );
};

// Example 6: Component that shows how data is automatically shared
export const SharedDataExample = () => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">
        Multiple Components Using Same Data (No Extra API Calls)
      </h3>
      
      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Component 1: Counter</CardTitle>
          </CardHeader>
          <CardContent>
            <TrunkCounter />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Component 2: Quick Info</CardTitle>
          </CardHeader>
          <CardContent>
            <QuickTrunkInfo />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Component 3: Dashboard</CardTitle>
          </CardHeader>
          <CardContent>
            <TrunkStatusDashboard />
          </CardContent>
        </Card>
      </div>
      
      <p className="text-sm text-gray-600">
        ℹ️ All these components share the same cached data. The API is only called once!
      </p>
    </div>
  );
};
