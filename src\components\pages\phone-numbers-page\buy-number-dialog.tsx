"use client";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { <PERSON><PERSON>, DialogTrigger, DialogContent, DialogTitle, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Input } from "@/components/ui/input";
import { Info } from "lucide-react";

interface BuyNumberDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  titleLabel?: string;
  providerLabel?: string;
  areaCodeLabel?: string;
  areaCodePlaceholder?: string;
  optionalLabel?: string;
  monthlyFeeLabel?: string;
  cancelLabel?: string;
  saveLabel?: string;
}

export default function BuyNumberDialog({ open, setOpen }: BuyNumberDialogProps) {
  const t = useTranslations();
  const [provider, setProvider] = useState("Twilio");
  const [areaCode, setAreaCode] = useState("");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogTitle>{t("phoneNumbersPage.buyPhoneNumber")}</DialogTitle>
        <div className="mt-4">
          <div className="mb-2 font-medium">{t("phoneNumbersPage.provider")}</div>
          <RadioGroup className="flex gap-2 mb-4 w-full" value={provider} onValueChange={setProvider}>
            <label htmlFor="twilio" className={`flex-1 flex items-center justify-between border rounded-md px-4 py-2 cursor-pointer transition-colors ${provider === "Twilio" ? "border-black" : "border-gray-200"}`}>
              <span>Twilio</span>
              <RadioGroupItem value="Twilio" id="twilio" className="ml-4" />
            </label>
            <label htmlFor="telnyx" className={`flex-1 flex items-center justify-between border rounded-md px-4 py-2 cursor-pointer transition-colors ${provider === "Telnyx" ? "border-black" : "border-gray-200"}`}>
              <span>Telnyx</span>
              <RadioGroupItem value="Telnyx" id="telnyx" className="ml-4" />
            </label>
          </RadioGroup>
          <div className="mb-2 font-medium">{t("phoneNumbersPage.areaCode")} <span className="text-gray-400 font-normal">({t("phoneNumbersPage.optional")})</span></div>
          <Input placeholder={t("phoneNumbersPage.areaCodePlaceholder")}
            value={areaCode}
            onChange={e => setAreaCode(e.target.value)}
            className="mb-4"
          />
          <div className="flex items-center gap-2 bg-muted/50 rounded-md px-3 py-2 mb-4">
            <Info className="w-4 h-4 text-gray-500" />
            <span className="text-xs">{t("phoneNumbersPage.monthlyFee")} $2.00.</span>
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" className="cursor-pointer">{t("common.cancel")}</Button>
          </DialogClose>
          <Button className="bg-black text-white hover:bg-neutral-800 cursor-pointer">{t("common.save")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 